class Product {
  final int? id;
  final String productCode;
  final String productName;
  final String imageUrl;
  final DateTime createdAt;

  Product({
    this.id,
    required this.productCode,
    required this.productName,
    required this.imageUrl,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'product_code': productCode,
      'product_name': productName,
      'image_url': imageUrl,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory Product.fromMap(Map<String, dynamic> map) {
    return Product(
      id: map['id']?.toInt(),
      productCode: map['product_code'] ?? '',
      productName: map['product_name'] ?? '',
      imageUrl: map['image_url'] ?? '',
      createdAt: DateTime.fromMillisecondsSinceEpoch(map['created_at']),
    );
  }

  Product copyWith({
    int? id,
    String? productCode,
    String? productName,
    String? imageUrl,
    DateTime? createdAt,
  }) {
    return Product(
      id: id ?? this.id,
      productCode: productCode ?? this.productCode,
      productName: productName ?? this.productName,
      imageUrl: imageUrl ?? this.imageUrl,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'Product(id: $id, productCode: $productCode, productName: $productName, imageUrl: $imageUrl, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product &&
        other.id == id &&
        other.productCode == productCode &&
        other.productName == productName &&
        other.imageUrl == imageUrl &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        productCode.hashCode ^
        productName.hashCode ^
        imageUrl.hashCode ^
        createdAt.hashCode;
  }
}
